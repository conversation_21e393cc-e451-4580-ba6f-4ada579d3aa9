import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';

// GET /api/career-paths/bookmarks - Get user's bookmarked career paths
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Get user's bookmarked career paths
    const bookmarks = await prisma.careerPathBookmark.findMany({
      where: {
        userId: session.user.id,
      },
      include: {
        careerPath: {
          include: {
            learningResources: {
              where: { isActive: true },
              select: {
                id: true,
                title: true,
                skillLevel: true,
                cost: true
              },
              take: 3
            },
            relatedSkills: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await prisma.careerPathBookmark.count({
      where: {
        userId: session.user.id,
      },
    });

    // Format the response
    const formattedBookmarks = bookmarks.map(bookmark => ({
      id: bookmark.id,
      bookmarkedAt: bookmark.createdAt,
      careerPath: {
        ...bookmark.careerPath,
        pros: JSON.parse(bookmark.careerPath.pros),
        cons: JSON.parse(bookmark.careerPath.cons),
        actionableSteps: Array.isArray(bookmark.careerPath.actionableSteps) 
          ? bookmark.careerPath.actionableSteps 
          : JSON.parse(bookmark.careerPath.actionableSteps || '[]'),
        isBookmarked: true
      }
    }));

    return NextResponse.json({
      bookmarks: formattedBookmarks,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching bookmarked career paths:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/career-paths/bookmarks - Remove bookmark
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { careerPathId } = body;

    if (!careerPathId) {
      return NextResponse.json(
        { error: 'Career path ID is required' },
        { status: 400 }
      );
    }

    // Remove bookmark
    const deletedBookmark = await prisma.careerPathBookmark.deleteMany({
      where: {
        userId: session.user.id,
        careerPathId,
      },
    });

    if (deletedBookmark.count === 0) {
      return NextResponse.json(
        { error: 'Bookmark not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Bookmark removed successfully'
    });

  } catch (error) {
    console.error('Error removing bookmark:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
